import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.santillana.agendafamiliar',
  appName: 'Agenda Familiar',
  webDir: 'dist',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    Browser: {
      // Configure Browser plugin for OIDC authentication
      presentationStyle: 'popover'
    },
    App: {
      // Handle deep links for OIDC callbacks
      deepLinkingEnabled: true
    }
  }
};

export default config;
